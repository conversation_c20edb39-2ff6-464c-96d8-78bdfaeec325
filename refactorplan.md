You're looking for a more explicit and flexible design where the codebase files are *always* provided as part of the tool call, rather than relying on a server-startup default. This makes the tool's behavior more predictable and allows for dynamic context switching per query.

This is a good design choice for an MCP server, as it gives the calling agent (the IDE's AI) full control over the context for each specific request.

Here's how we would modify the code to achieve this, making `codebase_files` a *required* parameter for the `deepview` tool and removing the server-startup default:

### Core Changes Required

1.  **Remove Global Codebase State:** Eliminate `global_codebases` and `load_codebase_from_file` from `deepview_mcp/server.py`.
2.  **Make `codebase_files` Required:** Change the `deepview` tool signature to `codebase_files: List[str]`.
3.  **Update CLI:** Remove the `codebase_file` argument from `deepview_mcp/cli.py` as the server will no longer load a default.
4.  **Update `test.py`:** Ensure `codebase_files` is always passed to the `deepview` tool call.
5.  **Update Documentation (`README.md`, `smithery.yaml`):** Reflect the new usage.

---

### Detailed Code Modifications

#### 1. `deepview_mcp/server.py`

```python
import os
import logging
import sys
import google.generativeai as genai
from dotenv import load_dotenv
from typing import Dict, Any, List, Tuple # Add List and Tuple

# Configure logging to stderr instead of file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Gemini API key from environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    logger.error("GEMINI_API_KEY environment variable not set")
    raise ValueError("GEMINI_API_KEY environment variable must be set")

# Configure Gemini API
genai.configure(api_key=GEMINI_API_KEY)

# --- REMOVED: global_codebases and load_codebase_from_file ---
# These are no longer needed as codebase files will always be passed per tool call.

def _read_file_content(file_path: str) -> str:
    """Helper to read content of a single file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
        raise

def load_codebases_from_paths(file_paths: List[str]) -> Dict[str, str]:
    """Load content from a list of file paths into a dictionary."""
    loaded_content: Dict[str, str] = {}
    if not file_paths:
        raise ValueError("No file paths provided to load.")

    for file_path in file_paths:
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}. Skipping.")
            continue # Skip non-existent files
        try:
            content = _read_file_content(file_path)
            loaded_content[file_path] = content
            logger.info(f"Loaded file: {file_path}, size: {len(content)} characters")
        except Exception as e:
            logger.warning(f"Skipping file {file_path} due to error: {str(e)}")
            # Decide if you want to fail entirely or just skip problematic files
            # For now, we'll skip and let the tool return an error if no files load.
    return loaded_content

def create_mcp_server(model_name="gemini-2.0-flash-lite"):
    """Create and configure the MCP server.
    
    Args:
        model_name: The Gemini model to use for queries
    
    Returns:
        An MCP server instance
    """
    from mcp.server.fastmcp import FastMCP
    
    mcp_server = FastMCP("DeepView MCP")
    
    @mcp_server.tool()
    # CHANGED: codebase_files is now REQUIRED (no default None)
    def deepview(question: str, codebase_files: List[str]) -> Dict[str, Any]:
        """
        Ask a question about the codebase using Gemini.
        
        Args:
            question: The question to ask about the codebase
            codebase_files: A list of paths to codebase files to load for the query.
                            At least one file path must be provided.
        
        Returns:
            Dictionary with the query result or error
        """
        # CHANGED: No more global_codebases check. codebase_files is now required.
        if not codebase_files:
            logger.error("No codebase files provided as tool parameter.")
            return {"error": "No codebase files provided. Please specify at least one file path."}

        current_codebases: Dict[str, str] = {}
        try:
            current_codebases = load_codebases_from_paths(codebase_files)
        except ValueError as e: # Catch the specific error from load_codebases_from_paths
            logger.error(f"Error loading codebases: {str(e)}")
            return {"error": f"Error loading codebases: {str(e)}"}
        except Exception as e:
            logger.error(f"Failed to load one or more codebases from parameter: {str(e)}")
            return {"error": f"Failed to load codebase files: {str(e)}"}
        
        # Check if any files were successfully loaded
        if not current_codebases:
            return {"error": "No valid codebase files could be loaded from the provided paths."}
        
        # Create prompt for Gemini
        system_prompt = (
            "You are a diligent programming assistant analyzing code. Your task is to "
            "answer questions about the provided code repository accurately and in detail. "
            "Always include specific references to files, functions, and class names in your "
            "responses. When comparing files, explicitly mention the file paths. "
            "At the end, list related files, functions, and classes that could be "
            "potentially relevant to the question, explaining their relevance."
        )

        # Construct the CODE_REPOSITORY block for multiple files
        code_repository_block = ""
        for file_path, content in current_codebases.items():
            filename = os.path.basename(file_path)
            
            if filename.endswith(".py"):
                lang = "python"
            elif filename.endswith(".js") or filename.endswith(".ts"):
                lang = "javascript"
            elif filename.endswith(".java"):
                lang = "java"
            elif filename.endswith(".xml"):
                lang = "xml"
            elif filename.endswith(".md"): # Added markdown for README
                lang = "markdown"
            else:
                lang = ""

            code_repository_block += f"""
<FILE_START path="{file_path}">
```<{lang}>
{content}
```
<FILE_END>
"""

        user_prompt = f"""
Below is the content of a code repository, potentially split into multiple files.
Each file is clearly marked with <FILE_START path="filename"> and <FILE_END>.

Please answer the following question about the code:

<QUESTION>
{question}
</QUESTION>

<CODE_REPOSITORY>
{code_repository_block}
</CODE_REPOSITORY>"""

        try:
            # Use Gemini to generate a response
            logger.info(f"Using Gemini model: {model_name}")
            model = genai.GenerativeModel(model_name, system_instruction=system_prompt)
            response = model.generate_content(user_prompt)
            
            return response.text
        except Exception as e:
            logger.error(f"Error querying {model_name}: {str(e)}")
            return {"error": f"Failed to query {model_name}: {str(e)}"}
    
    return mcp_server
```

#### 2. `deepview_mcp/cli.py`

```python
"""
Command-line interface for DeepView MCP.
"""

import sys
import argparse
import logging
# Removed: from .server import load_codebase_from_file # No longer needed

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="DeepView MCP - A Model Context Protocol server for analyzing large codebases")
    # REMOVED: codebase_file positional argument
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], 
                        default="INFO", help="Set the logging level")
    parser.add_argument("--model", type=str, default="gemini-2.0-flash-lite",
                        help="Gemini model to use (default: gemini-2.0-flash-lite)")
    return parser.parse_args()

def main():
    """Main entry point for the CLI."""
    args = parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        stream=sys.stderr
    )
    
    # REMOVED: Codebase loading logic from CLI
    logger.info("DeepView MCP server started. Codebase files must be provided as a parameter to the 'deepview' tool call.")
    
    # Create and run MCP server
    from .server import create_mcp_server # Import specifically
    mcp_server = create_mcp_server(model_name=args.model)
    logger.info(f"Starting MCP server with model: {args.model}")
    mcp_server.run()

if __name__ == "__main__":
    main()
```

#### 3. `deepview_mcp/test.py`

```python
import sys
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import asyncio
from contextlib import AsyncExitStack
from dotenv import load_dotenv

"""
DeepView MCP Test Script

This script demonstrates how to use the DeepView MCP client to query a codebase
using the Gemini model. It explicitly passes codebase file paths to the tool call.

Usage:
    python test.py "Your question about the codebase here"
"""

# Load environment variables from .env file
load_dotenv()

# Check for required environment variables
if not os.environ.get("GEMINI_API_KEY"):
    print("Error: GEMINI_API_KEY environment variable not found.")
    print("Please add your Gemini API key to the .env file:")
    print("GEMINI_API_KEY=your_api_key_here")
    sys.exit(1)

# --- Create dummy files for testing multiple inputs ---
def create_dummy_files():
    temp_dir = "temp_codebase_for_test" # Changed directory name to avoid conflict
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    file1_path = os.path.join(temp_dir, "main.py")
    file2_path = os.path.join(temp_dir, "utils.py")
    file3_path = os.path.join(temp_dir, "README.md")

    with open(file1_path, "w") as f:
        f.write("""
# main.py
def calculate_sum(a, b):
    \"\"\"Calculates the sum of two numbers.\"\"\"
    return a + b

def main():
    print(f"Sum: {calculate_sum(10, 20)}")

if __name__ == "__main__":
    main()
""")
    with open(file2_path, "w") as f:
        f.write("""
# utils.py
def multiply(x, y):
    \"\"\"Multiplies two numbers.\"\"\"
    return x * y

def divide(x, y):
    \"\"\"Divides two numbers, handles division by zero.\"\"\"
    if y == 0:
        return "Error: Division by zero"
    return x / y
""")
    with open(file3_path, "w") as f:
        f.write("""
# README.md
This is a simple Python project demonstrating basic arithmetic operations.
- `main.py`: Entry point, calculates sum.
- `utils.py`: Contains utility functions for multiply and divide.
""")
    return [file1_path, file2_path, file3_path]

# --- End dummy file creation ---

async def async_main():
    # Get question from command line arguments
    question = "Compare the `calculate_sum` function in `main.py` with the `multiply` function in `utils.py`. What are their purposes and how do they differ?" # default question
    if len(sys.argv) > 1:
        question = sys.argv[1]
      
    # Create dummy files and get their paths
    CODEBASE_FILES = create_dummy_files()

    # Set up server parameters
    # IMPORTANT: No codebase files are passed to the server command directly.
    # They are exclusively passed as a tool argument.
    server_params = StdioServerParameters(
        command="deepview-mcp",
        args=[], # Empty args, no default codebase file
        env=os.environ.copy()
    )
    
    # Create exit stack for resource management
    async with AsyncExitStack() as stack:
        print(f"Starting server...", file=sys.stderr)
        
        # Connect to server via stdio transport
        stdio_transport = await stack.enter_async_context(stdio_client(server_params))
        stdio, write = stdio_transport
        
        # Create client session
        session = await stack.enter_async_context(ClientSession(stdio, write))
        
        # Initialize the session
        await session.initialize()
        
        # List available tools
        print("Listing available tools...", file=sys.stderr)
        try:
            list_tools_response = await session.list_tools()
            print("Available Tools:", [tool.name for tool in list_tools_response.tools], file=sys.stderr)
        except Exception as e:
            print(f"Error listing tools: {str(e)}", file=sys.stderr)
            return 1
        
        # Query the codebase
        print(f"Querying codebase with question: '{question}'", file=sys.stderr)
        try:
            # Call the deepview tool, passing multiple codebase files as a REQUIRED argument
            call_response = await session.call_tool(
                "deepview", 
                {"question": question, "codebase_files": CODEBASE_FILES} # Pass the list of files here
            )
            
            # Print the result
            if call_response is not None:
                print("Raw Response:", call_response, file=sys.stderr) # For debugging
                
                if hasattr(call_response, 'content') and call_response.content:
                    if isinstance(call_response.content, list):
                        for item in call_response.content:
                            if hasattr(item, 'text'):
                                print(item.text)
                    else:
                        print(call_response.content)
                else:
                    print("No content found in response")
            else:
                print("Error: No response received from server")
                
        except Exception as e:
            print(f"Error querying codebase: {str(e)}", file=sys.stderr)
            return 1
    
    # Clean up dummy files
    temp_dir = os.path.dirname(CODEBASE_FILES[0])
    for f_path in CODEBASE_FILES:
        os.remove(f_path)
    os.rmdir(temp_dir)
    
    return 0

def main():
    return asyncio.run(async_main())

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python test.py \"Your question about the codebase here\"")
        print("Example:")
        print("  python test.py \"Compare the `calculate_sum` function in `main.py` with the `multiply` function in `utils.py`.\"")
    
    sys.exit(main())
```

#### 4. `README.md`

Update the "Usage" section:

```markdown
## Usage

### Starting the Server

Note: you don't need to start the server manually. These parameters are configured in your MCP setup in your IDE (see below).

```bash
# Basic usage with default model and log level
deepview-mcp

# Specify a different Gemini model
deepview-mcp --model gemini-2.0-pro

# Change log level
deepview-mcp --log-level DEBUG
```

**Important:** Codebase files are now *always* provided as a parameter to the `deepview` tool call, not when starting the server.

### Command-line Options

- `--model MODEL`: Specify the Gemini model to use (default: gemini-2.0-flash-lite)
- `--log-level {DEBUG,INFO,WARNING,ERROR,CRITICAL}`: Set the logging level (default: INFO)

### Using with an IDE (Cursor/Windsurf/...)

1.  Open IDE settings
2.  Navigate to the MCP configuration
3.  Add a new MCP server with the following configuration:
    ```json
    {
      "mcpServers": {
        "deepview": {
          "command": "/path/to/deepview-mcp",
          "args": [], // No codebase file here
          "env": {
            "GEMINI_API_KEY": "your_gemini_api_key"
          }
        }
      }
    }
    ```

Here's how to specify the Gemini version to use:

```json
{
   "mcpServers": {
     "deepview": {
       "command": "/path/to/deepview-mcp",
       "args": ["--model", "gemini-2.5-pro-exp-03-25"],
       "env": {
         "GEMINI_API_KEY": "your_gemini_api_key"
       }
     }
   }
}
```

4.  Reload MCP servers configuration

### Available Tools

The server provides one tool:

1.  `deepview`: Ask a question about the codebase
    - Required parameter: `question` - The question to ask about the codebase
    - Required parameter: `codebase_files` - **A list of paths** to codebase files to load for the query.

## Preparing Your Codebase

DeepView MCP requires files containing your codebase. You can use [repomix](https://github.com/yamadashy/repomix) to prepare your codebase in an AI-friendly format. You will then pass the paths to these generated files (or any other text files) directly to the `deepview` tool.
```

#### 5. `smithery.yaml`

```yaml
# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - geminiApiKey
    properties:
      geminiApiKey:
        type: string
        description: Gemini API Key from Google AI Studio
      # REMOVED: codebaseFile property
      model:
        type: string
        default: gemini-2.0-flash-lite
        description: Gemini model to use. For example, gemini-2.0-flash-lite or
          gemini-2.5-pro-exp-03-25.
      logLevel:
        type: string
        default: INFO
        description: Logging level for the server.
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => { 
      const args = [];
      // REMOVED: codebaseFile arg logic
      if(config.model && config.model.trim() !== '') {
        args.push('--model', config.model);
      }
      if(config.logLevel && config.logLevel.trim() !== '') {
        args.push('--log-level', config.logLevel);
      }
      return {
        command: 'deepview-mcp',
        args: args,
        env: { GEMINI_API_KEY: config.geminiApiKey }
      };
    }
  exampleConfig:
    geminiApiKey: EXAMPLE_GEMINI_API_KEY
    # REMOVED: codebaseFile example
    model: gemini-2.5-pro-exp-03-25
    logLevel: INFO
```

---

### Summary of the New Behavior

*   **Server Startup:** The `deepview-mcp` server now starts without any default codebase loaded. Its only purpose at startup is to listen for MCP requests.
*   **Tool Call:** When an MCP client (like an IDE) calls the `deepview` tool, it *must* provide a `codebase_files` parameter, which is a list of file paths.
*   **Dynamic Context:** This design ensures that every `deepview` tool call explicitly defines its context (the files it needs to analyze), making it highly flexible for tasks like comparing different files or analyzing subsets of a larger project.
*   **Clearer Responsibility:** The responsibility for providing the relevant files now clearly lies with the calling agent/client, not with the server's initial configuration.