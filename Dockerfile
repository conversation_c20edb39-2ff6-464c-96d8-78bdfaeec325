# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
FROM python:3.13-slim

WORKDIR /app

# Copy necessary files
COPY pyproject.toml poetry.lock* /app/
COPY deepview_mcp /app/deepview_mcp
COPY LICENSE README.md compress.py requirements.txt setup.py /app/

# Install Poetry and project dependencies
RUN pip install poetry \
    && poetry config virtualenvs.create false \
    && poetry install --no-interaction --no-ansi

# Create a dummy codebase file so that if a non-empty codebaseFile is provided but still not mounted, server doesn't fail immediately.
RUN mkdir -p /data && touch /data/dummy-codebase.txt

# Expose any port if needed (optional)

CMD [ "deepview-mcp" ]
