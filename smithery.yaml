# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - geminiApiKey
    properties:
      geminiApiKey:
        type: string
        description: Gemini API Key from Google AI Studio
      model:
        type: string
        default: gemini-2.0-flash-lite
        description: Gemini model to use. For example, gemini-2.0-flash-lite or
          gemini-2.5-pro-exp-03-25.
      logLevel:
        type: string
        default: INFO
        description: Logging level for the server. Use absolute file paths when calling the deepview tool.
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => {
      const args = [];
      if(config.model && config.model.trim() !== '') {
        args.push('--model', config.model);
      }
      if(config.logLevel && config.logLevel.trim() !== '') {
        args.push('--log-level', config.logLevel);
      }
      return {
        command: 'deepview-mcp',
        args: args,
        env: { GEMINI_API_KEY: config.geminiApiKey }
      };
    }
  exampleConfig:
    geminiApiKey: EXAMPLE_GEMINI_API_KEY
    model: gemini-2.5-pro-exp-03-25
    logLevel: INFO
