"""
Command-line interface for DeepView MCP.
"""

import sys
import argparse
import logging

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="DeepView MCP - A Model Context Protocol server for analyzing large codebases")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                        default="INFO", help="Set the logging level")
    parser.add_argument("--model", type=str, default="gemini-2.0-flash-lite",
                        help="Gemini model to use (default: gemini-2.0-flash-lite)")
    parser.add_argument("--project-root", type=str,
                        help="Project root directory (defaults to current working directory)")
    return parser.parse_args()

def main():
    """Main entry point for the CLI."""
    args = parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        stream=sys.stderr
    )
    
    # Set project root if provided
    if args.project_root:
        import os
        # Handle special keywords
        if args.project_root.lower() in ['{current_dir}', 'current_dir', 'cwd']:
            actual_root = os.getcwd()
            os.environ['DEEPVIEW_PROJECT_ROOT'] = actual_root
            logger.info(f"Using current working directory as project root: {actual_root}")
        else:
            os.environ['DEEPVIEW_PROJECT_ROOT'] = args.project_root
            logger.info(f"Using project root from argument: {args.project_root}")

    logger.info("DeepView MCP server started. Codebase files must be provided as a parameter to the 'deepview' tool call.")

    # Create and run MCP server
    from .server import create_mcp_server
    mcp_server = create_mcp_server(model_name=args.model)
    logger.info(f"Starting MCP server with model: {args.model}")
    mcp_server.run()

if __name__ == "__main__":
    main()
