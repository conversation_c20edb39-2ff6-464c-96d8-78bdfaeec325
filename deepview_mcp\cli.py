"""
Command-line interface for DeepView MCP.
"""

import sys
import argparse
import logging

logger = logging.getLogger(__name__)

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="DeepView MCP - A Model Context Protocol server for analyzing large codebases")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                        default="INFO", help="Set the logging level")
    parser.add_argument("--model", type=str, default="gemini-2.0-flash-lite",
                        help="Gemini model to use (default: gemini-2.0-flash-lite)")
    return parser.parse_args()

def main():
    """Main entry point for the CLI."""
    args = parse_args()
    
    # Configure logging
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        stream=sys.stderr
    )
    
    logger.info("DeepView MCP server started. Use absolute file paths for best reliability.")

    # Create and run MCP server
    from .server import create_mcp_server
    mcp_server = create_mcp_server(model_name=args.model)
    logger.info(f"Starting MCP server with model: {args.model}")
    mcp_server.run()

if __name__ == "__main__":
    main()
