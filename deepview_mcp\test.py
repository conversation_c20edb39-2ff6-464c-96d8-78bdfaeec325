import sys
import os
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import asyncio
from contextlib import AsyncExitStack
from dotenv import load_dotenv

"""
DeepView MCP Test Script

This script demonstrates how to use the DeepView MCP client to query a codebase
using the Gemini model. It explicitly passes codebase file paths to the tool call.

Usage:
    python test.py "Your question about the codebase here"
"""

# Load environment variables from .env file
load_dotenv()

# Check for required environment variables
if not os.environ.get("GEMINI_API_KEY"):
    print("Error: GEMINI_API_KEY environment variable not found.")
    print("Please add your Gemini API key to the .env file:")
    print("GEMINI_API_KEY=your_api_key_here")
    sys.exit(1)

# --- Create dummy files for testing multiple inputs ---
def create_dummy_files():
    temp_dir = "temp_codebase_for_test" # Changed directory name to avoid conflict
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    file1_path = os.path.join(temp_dir, "main.py")
    file2_path = os.path.join(temp_dir, "utils.py")
    file3_path = os.path.join(temp_dir, "README.md")

    with open(file1_path, "w") as f:
        f.write("""
# main.py
def calculate_sum(a, b):
    \"\"\"Calculates the sum of two numbers.\"\"\"
    return a + b

def main():
    print(f"Sum: {calculate_sum(10, 20)}")

if __name__ == "__main__":
    main()
""")
    with open(file2_path, "w") as f:
        f.write("""
# utils.py
def multiply(x, y):
    \"\"\"Multiplies two numbers.\"\"\"
    return x * y

def divide(x, y):
    \"\"\"Divides two numbers, handles division by zero.\"\"\"
    if y == 0:
        return "Error: Division by zero"
    return x / y
""")
    with open(file3_path, "w") as f:
        f.write("""
# README.md
This is a simple Python project demonstrating basic arithmetic operations.
- `main.py`: Entry point, calculates sum.
- `utils.py`: Contains utility functions for multiply and divide.
""")
    return [file1_path, file2_path, file3_path]

# --- End dummy file creation ---

async def async_main():
    # Get question from command line arguments
    question = "Compare the `calculate_sum` function in `main.py` with the `multiply` function in `utils.py`. What are their purposes and how do they differ?" # default question
    if len(sys.argv) > 1:
        question = sys.argv[1]

    # Create dummy files and get their paths
    CODEBASE_FILES = create_dummy_files()

    # Set up server parameters
    # IMPORTANT: No codebase files are passed to the server command directly.
    # They are exclusively passed as a tool argument.
    server_params = StdioServerParameters(
        command=sys.executable,
        args=["-m", "deepview_mcp.cli"], # Empty args, no default codebase file
        env=os.environ.copy()
    )
    
    # Create exit stack for resource management
    async with AsyncExitStack() as stack:
        print(f"Starting server...", file=sys.stderr)
        
        # Connect to server via stdio transport
        stdio_transport = await stack.enter_async_context(stdio_client(server_params))
        stdio, write = stdio_transport
        
        # Create client session
        session = await stack.enter_async_context(ClientSession(stdio, write))
        
        # Initialize the session
        await session.initialize()
        
        # List available tools
        print("Listing available tools...", file=sys.stderr)
        try:
            list_tools_response = await session.list_tools()
            print("Available Tools:", [tool.name for tool in list_tools_response.tools], file=sys.stderr)
        except Exception as e:
            print(f"Error listing tools: {str(e)}", file=sys.stderr)
            return 1

        # Query the codebase
        print(f"Querying codebase with question: '{question}'", file=sys.stderr)
        try:
            # Call the deepview tool, passing multiple codebase files as a REQUIRED argument
            call_response = await session.call_tool(
                "deepview",
                {"question": question, "codebase_files": CODEBASE_FILES} # Pass the list of files here
            )
            
            # Print the result
            if call_response is not None:
                print("Raw Response:", call_response, file=sys.stderr) # For debugging

                if hasattr(call_response, 'content') and call_response.content:
                    if isinstance(call_response.content, list):
                        for item in call_response.content:
                            if hasattr(item, 'text'):
                                print(item.text)
                    else:
                        print(call_response.content)
                else:
                    print("No content found in response")
            else:
                print("Error: No response received from server")

        except Exception as e:
            print(f"Error querying codebase: {str(e)}", file=sys.stderr)
            return 1

    # Clean up dummy files
    temp_dir = os.path.dirname(CODEBASE_FILES[0])
    for f_path in CODEBASE_FILES:
        os.remove(f_path)
    os.rmdir(temp_dir)

    return 0

def main():
    return asyncio.run(async_main())

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python test.py \"Your question about the codebase here\"")
        print("Example:")
        print("  python test.py \"Compare the `calculate_sum` function in `main.py` with the `multiply` function in `utils.py`.\"")
        sys.exit(1)
    
    sys.exit(main())
