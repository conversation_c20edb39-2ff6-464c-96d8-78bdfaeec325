"""
DeepView MCP Server - A Model Context Protocol server implementation
for analyzing large codebases using Gemini 1.5 Pro.
"""

import os
import logging
import sys
import google.generativeai as genai
from dotenv import load_dotenv
from typing import Dict, Any, List, Tuple

# Configure logging to stderr instead of file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr  # Log to stderr instead of file
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Gemini API key from environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    logger.error("GEMINI_API_KEY environment variable not set")
    raise ValueError("GEMINI_API_KEY environment variable must be set")

# Configure Gemini API
genai.configure(api_key=GEMINI_API_KEY)

def _read_file_content(file_path: str) -> str:
    """Helper to read content of a single file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
        raise

# Removed complex project root detection - now using simple current working directory approach

def load_codebases_from_paths(file_paths: List[str]) -> Dict[str, str]:
    """
    Load content from a list of file paths into a dictionary.

    IMPORTANT: Absolute paths are strongly recommended for reliability.
    Relative paths will be attempted against the current working directory but may not work
    if the server process runs from a different directory than expected.
    """
    loaded_content: Dict[str, str] = {}
    if not file_paths:
        raise ValueError("No file paths provided to load.")

    current_dir = os.getcwd()
    logger.info(f"Loading files from current working directory: {current_dir}")

    for file_path in file_paths:
        # Use absolute path directly, or resolve relative path against current directory
        if os.path.isabs(file_path):
            absolute_path = file_path
        else:
            absolute_path = os.path.join(current_dir, file_path)
            logger.warning(f"Relative path detected: '{file_path}'. Using absolute path: '{absolute_path}'. For reliability, use absolute paths.")

        if not os.path.exists(absolute_path):
            logger.warning(f"File not found: '{absolute_path}'. Skipping.")
            continue

        try:
            content = _read_file_content(absolute_path)
            loaded_content[file_path] = content
            logger.info(f"Loaded file: '{file_path}' -> '{absolute_path}', size: {len(content)} characters")
        except Exception as e:
            logger.warning(f"Skipping file '{file_path}' due to error: {str(e)}")

    if not loaded_content:
        raise ValueError("No files could be loaded from the provided paths.")

    return loaded_content

def create_mcp_server(model_name="gemini-2.0-flash-lite"):
    """Create and configure the MCP server.
    
    Args:
        model_name: The Gemini model to use for queries
    
    Returns:
        An MCP server instance
    """
    from mcp.server.fastmcp import FastMCP
    
    mcp_server = FastMCP("DeepView MCP")
    
    @mcp_server.tool()
    def deepview(question: str, codebase_files: List[str]) -> Dict[str, Any]:
        """
        Ask a question about the codebase using Gemini.

        Args:
            question: The question to ask about the codebase
            codebase_files: A list of paths to codebase files to load for the query.
                           IMPORTANT: Use ABSOLUTE file paths for reliability.
                           Relative paths may not work if the server runs from a different directory.
                           At least one file path must be provided.

                           Examples:
                           - ✅ Good: ["/home/<USER>/project/src/main.py", "/home/<USER>/project/README.md"]
                           - ✅ Good: ["C:\\Users\\<USER>\\project\\src\\main.py", "C:\\Users\\<USER>\\project\\README.md"]
                           - ⚠️  Risky: ["src/main.py", "README.md"] (may not work reliably)

        Returns:
            Dictionary with the query result or error
        """
        # No more global_codebases check. codebase_files is now required.
        if not codebase_files:
            logger.error("No codebase files provided as tool parameter.")
            return {"error": "No codebase files provided. Please specify at least one file path."}

        current_codebases: Dict[str, str] = {}
        try:
            current_codebases = load_codebases_from_paths(codebase_files)
        except ValueError as e: # Catch the specific error from load_codebases_from_paths
            logger.error(f"Error loading codebases: {str(e)}")
            return {"error": f"Error loading codebases: {str(e)}"}
        except Exception as e:
            logger.error(f"Failed to load one or more codebases from parameter: {str(e)}")
            return {"error": f"Failed to load codebase files: {str(e)}"}

        # Check if any files were successfully loaded
        if not current_codebases:
            return {"error": "No valid codebase files could be loaded from the provided paths."}
        
        # Create prompt for Gemini
        system_prompt = (
            "You are a diligent programming assistant analyzing code. Your task is to "
            "answer questions about the provided code repository accurately and in detail. "
            "Always include specific references to files, functions, and class names in your "
            "responses. When comparing files, explicitly mention the file paths. "
            "At the end, list related files, functions, and classes that could be "
            "potentially relevant to the question, explaining their relevance."
        )

        # Construct the CODE_REPOSITORY block for multiple files
        code_repository_block = ""
        for file_path, content in current_codebases.items():
            filename = os.path.basename(file_path)

            if filename.endswith(".py"):
                lang = "python"
            elif filename.endswith(".js") or filename.endswith(".ts"):
                lang = "javascript"
            elif filename.endswith(".java"):
                lang = "java"
            elif filename.endswith(".xml"):
                lang = "xml"
            elif filename.endswith(".md"): # Added markdown for README
                lang = "markdown"
            else:
                lang = ""

            code_repository_block += f"""
<FILE_START path="{file_path}">
```{lang}
{content}
```
<FILE_END>
"""

        user_prompt = f"""
Below is the content of a code repository, potentially split into multiple files.
Each file is clearly marked with <FILE_START path="filename"> and <FILE_END>.

Please answer the following question about the code:

<QUESTION>
{question}
</QUESTION>

<CODE_REPOSITORY>
{code_repository_block}
</CODE_REPOSITORY>"""

        try:
            # Use Gemini to generate a response
            logger.info(f"Using Gemini model: {model_name}")
            model = genai.GenerativeModel(model_name, system_instruction=system_prompt)
            response = model.generate_content(user_prompt)
            
            return response.text
        except Exception as e:
            logger.error(f"Error querying {model_name}: {str(e)}")
            return {"error": f"Failed to query {model_name}: {str(e)}"}
    
    return mcp_server
