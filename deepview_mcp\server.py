"""
DeepView MCP Server - A Model Context Protocol server implementation
for analyzing large codebases using Gemini 1.5 Pro.
"""

import os
import logging
import sys
import google.generativeai as genai
from dotenv import load_dotenv
from typing import Dict, Any, List, Tuple

# Configure logging to stderr instead of file
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr  # Log to stderr instead of file
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Gemini API key from environment variables
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    logger.error("GEMINI_API_KEY environment variable not set")
    raise ValueError("GEMINI_API_KEY environment variable must be set")

# Configure Gemini API
genai.configure(api_key=GEMINI_API_KEY)

def _read_file_content(file_path: str) -> str:
    """Helper to read content of a single file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
        raise

def _find_project_root() -> str:
    """
    Find the project root directory dynamically.
    Priority: 1) CLI argument via env var, 2) Search from current dir, 3) Fallback to current dir
    """
    current_dir = os.getcwd()
    logger.info(f"Starting project root search from current directory: {current_dir}")

    # Strategy 1: Environment variable override (set by CLI --project-root argument)
    env_root = os.environ.get('DEEPVIEW_PROJECT_ROOT')
    if env_root and os.path.exists(env_root):
        logger.info(f"Using project root from CLI argument: {env_root}")
        return env_root

    # Look for common project files to identify the root
    project_markers = [
        # Version control
        '.git', '.hg', '.svn',
        # Python projects
        'pyproject.toml', 'setup.py', 'setup.cfg', 'requirements.txt', 'Pipfile', 'poetry.lock',
        # Node.js projects
        'package.json', 'package-lock.json', 'yarn.lock',
        # Other common project files
        'README.md', 'README.rst', 'README.txt',
        'Makefile', 'CMakeLists.txt', 'Dockerfile',
        # IDE/Editor files
        '.vscode', '.idea'
    ]

    # Strategy 2: Search upward from current working directory
    search_dir = current_dir
    for level in range(15):  # Search up to 15 levels up
        logger.debug(f"Checking directory (level {level}): {search_dir}")

        for marker in project_markers:
            marker_path = os.path.join(search_dir, marker)
            if os.path.exists(marker_path):
                logger.info(f"Found project root at: {search_dir} (marker: {marker})")
                return search_dir

        parent = os.path.dirname(search_dir)
        if parent == search_dir:  # Reached filesystem root
            logger.debug("Reached filesystem root, stopping search")
            break
        search_dir = parent

    # Strategy 3: Fallback to current directory
    logger.info(f"No project markers found, using current directory as root: {current_dir}")
    return current_dir

def load_codebases_from_paths(file_paths: List[str]) -> Dict[str, str]:
    """Load content from a list of file paths into a dictionary."""
    loaded_content: Dict[str, str] = {}
    if not file_paths:
        raise ValueError("No file paths provided to load.")

    # Find project root for resolving relative paths
    project_root = _find_project_root()
    current_dir = os.getcwd()
    logger.info(f"Current working directory: {current_dir}")
    logger.info(f"Using project root: {project_root}")

    for file_path in file_paths:
        # If path is relative, resolve it against project root
        if os.path.isabs(file_path):
            absolute_path = file_path
        else:
            absolute_path = os.path.join(project_root, file_path)

        if not os.path.exists(absolute_path):
            logger.warning(f"File not found: '{file_path}' (resolved to '{absolute_path}'). Skipping.")
            continue # Skip non-existent files
        try:
            # Read the file using the absolute path
            content = _read_file_content(absolute_path)

            # Store the content using the original path as the key, so the LLM context is consistent with user input
            loaded_content[file_path] = content
            logger.info(f"Loaded file: '{file_path}' -> '{absolute_path}', size: {len(content)} characters")
        except Exception as e:
            logger.warning(f"Skipping file '{file_path}' (resolved to '{absolute_path}') due to error: {str(e)}")
            # Decide if you want to fail entirely or just skip problematic files
            # For now, we'll skip and let the tool return an error if no files load.
    return loaded_content

def create_mcp_server(model_name="gemini-2.0-flash-lite"):
    """Create and configure the MCP server.
    
    Args:
        model_name: The Gemini model to use for queries
    
    Returns:
        An MCP server instance
    """
    from mcp.server.fastmcp import FastMCP
    
    mcp_server = FastMCP("DeepView MCP")
    
    @mcp_server.tool()
    def deepview(question: str, codebase_files: List[str]) -> Dict[str, Any]:
        """
        Ask a question about the codebase using Gemini.

        Args:
            question: The question to ask about the codebase
            codebase_files: A list of paths to codebase files to load for the query.
                            At least one file path must be provided.

        Returns:
            Dictionary with the query result or error
        """
        # No more global_codebases check. codebase_files is now required.
        if not codebase_files:
            logger.error("No codebase files provided as tool parameter.")
            return {"error": "No codebase files provided. Please specify at least one file path."}

        current_codebases: Dict[str, str] = {}
        try:
            current_codebases = load_codebases_from_paths(codebase_files)
        except ValueError as e: # Catch the specific error from load_codebases_from_paths
            logger.error(f"Error loading codebases: {str(e)}")
            return {"error": f"Error loading codebases: {str(e)}"}
        except Exception as e:
            logger.error(f"Failed to load one or more codebases from parameter: {str(e)}")
            return {"error": f"Failed to load codebase files: {str(e)}"}

        # Check if any files were successfully loaded
        if not current_codebases:
            return {"error": "No valid codebase files could be loaded from the provided paths."}
        
        # Create prompt for Gemini
        system_prompt = (
            "You are a diligent programming assistant analyzing code. Your task is to "
            "answer questions about the provided code repository accurately and in detail. "
            "Always include specific references to files, functions, and class names in your "
            "responses. When comparing files, explicitly mention the file paths. "
            "At the end, list related files, functions, and classes that could be "
            "potentially relevant to the question, explaining their relevance."
        )

        # Construct the CODE_REPOSITORY block for multiple files
        code_repository_block = ""
        for file_path, content in current_codebases.items():
            filename = os.path.basename(file_path)

            if filename.endswith(".py"):
                lang = "python"
            elif filename.endswith(".js") or filename.endswith(".ts"):
                lang = "javascript"
            elif filename.endswith(".java"):
                lang = "java"
            elif filename.endswith(".xml"):
                lang = "xml"
            elif filename.endswith(".md"): # Added markdown for README
                lang = "markdown"
            else:
                lang = ""

            code_repository_block += f"""
<FILE_START path="{file_path}">
```{lang}
{content}
```
<FILE_END>
"""

        user_prompt = f"""
Below is the content of a code repository, potentially split into multiple files.
Each file is clearly marked with <FILE_START path="filename"> and <FILE_END>.

Please answer the following question about the code:

<QUESTION>
{question}
</QUESTION>

<CODE_REPOSITORY>
{code_repository_block}
</CODE_REPOSITORY>"""

        try:
            # Use Gemini to generate a response
            logger.info(f"Using Gemini model: {model_name}")
            model = genai.GenerativeModel(model_name, system_instruction=system_prompt)
            response = model.generate_content(user_prompt)
            
            return response.text
        except Exception as e:
            logger.error(f"Error querying {model_name}: {str(e)}")
            return {"error": f"Failed to query {model_name}: {str(e)}"}
    
    return mcp_server
